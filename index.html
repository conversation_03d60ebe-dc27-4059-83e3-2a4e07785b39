<!doctype html>
<html class="no-js" lang="fr">
<head>
        <meta http-equiv="content-type" content="text/html; charset=UTF-8">
        <meta charset="utf-8">

        <!-- Page Title Here -->
        <title>Appart Hôtel Golden Sky Home – Café & Restaurant | Ouverture Prochaine</title>

        <!-- Page Description Here -->
		<meta name="description" content="Appart Hôtel Golden Sky Home – Café & Restaurant. Ouverture le 15 août 2025 à Béni <PERSON>lal, Maroc. Réservez dès maintenant !">

        <!-- Disable screen scaling-->
        <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1, maximum-scale=1, user-scalable=0">

        <!-- Place favicon.ico and apple-touch-icon(s) in the root directory -->

        <!-- Initializer -->
        <link rel="stylesheet" href="css/normalize.css">

        <!-- Google Fonts - Montserrat -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

        <!-- Web fonts and Web Icons -->
        <link rel="stylesheet" href="css/pageloader.css">
        <link rel="stylesheet" href="fonts/opensans/stylesheet.css">
        <link rel="stylesheet" href="fonts/asap/stylesheet.css">
        <link rel="stylesheet" href="css/ionicons.min.css">
        
        <!-- Vendor CSS style -->
        <link rel="stylesheet" href="css/foundation.min.css">
        <link rel="stylesheet" href="js/vendor/jquery.fullPage.css">
        <link rel="stylesheet" href="js/vegas/vegas.min.css">

		<!-- Main CSS files -->
        <link rel="stylesheet" href="css/main.css">
        <link rel="stylesheet" href="css/main_responsive.css">
        <link rel="stylesheet" href="css/style-color1.css">
        <link rel="stylesheet" href="css/golden-sky-theme.css">
        <link rel="stylesheet" href="css/browser-compatibility.css">

        <script src="js/vendor/modernizr-2.7.1.min.js"></script>
    </head>
    <body id="menu" class="alt-bg golden-sky-theme">
        <!--[if lt IE 8]>
            <p class="browsehappy">You are using an <strong>outdated</strong> browser. Please <a href="http://browsehappy.com/">upgrade your browser</a> to improve your experience.</p>
        <![endif]-->
        
        <!-- Page Loader -->
        <div class="page-loader" id="page-loader">
            <div><i class="ion ion-loading-d"></i><p>loading</p></div>
        </div>
        
        <!-- BEGIN OF site header Menu -->
		<!-- Add "material" class for a material design style -->
		<header class="header-top">

            <div class="menu clearfix">
                <a href="#about-us">à propos</a>
                <a href="#contact">contact</a>
                <div class="language-switcher">
                    <a href="#" id="lang-fr" class="lang-btn active">FR</a>
                    <span class="lang-separator">|</span>
                    <a href="#" id="lang-en" class="lang-btn">EN</a>
                </div>
            </div>
		</header>
        <!-- END OF site header Menu-->
        
        <!-- BEGIN OF Quick nav icons at left -->
		<nav class="quick-link count-4 nav-left">
			<div class="logo">
				<a href="#home">
					<img src="img/logo_only.png" alt="Logo Brand">
				</a>
			</div>
			<ul id="qmenu" class="qmenu">
				<li data-menuanchor="home">
					<a href="#home" class=""><i class="icon ion ion-home"></i>
					</a>
					<span class="title">Accueil</span>
				</li>
				<li data-menuanchor="register">
					<a href="#register"><i class="icon ion ion-calendar"></i>
					</a>
					<span class="title">Réservation</span>
				</li>
				<li data-menuanchor="about-us">
					<a href="#about-us"><i class="icon ion ion-information-circled"></i>
					</a>
					<span class="title">À Propos</span>
				</li>
				<li data-menuanchor="contact">
					<a href="#contact"><i class="icon ion ion-ios-telephone"></i>
					</a>
					<span class="title">Contact</span>
				</li>
			</ul>
		</nav>
        <!-- END OF Quick nav icons at left -->
        


        <!-- BEGIN OF site cover -->
        <div class="page-cover" id="s-cover">
            <!-- Cover Background -->
            <div class="cover-bg pos-abs full-size bg-img" data-image-src="img/bg-default.jpg"></div>
			
            <!-- BEGIN OF Slideshow Background -->
            <!--<div class="cover-bg pos-abs full-size slide-show">
				<i class='img' data-src='./img/bg-slide1.jpg'></i>
				<i class='img' data-src='./img/bg-slide2.jpg'></i>
				<i class='img' data-src='./img/bg-slide3.jpg'></i>
				<i class='img' data-src='./img/bg-slide4.jpg'></i>
			</div>-->
            <!-- END OF Slideshow Background -->
            
            <!--BEGIN OF Static video bg  - uncomment below to use Video as Background-->
            <!--<div id="container" class="video-container show-for-medium-up">
                <video autoplay="autoplay" loop="loop" autobuffer="autobuffer" muted="muted"
                       width="640" height="360">
                    <source src="vid/flower_loop.mp4" type="video/mp4">
                </video>
            </div>-->
            <!--END OF Static video bg-->
			
			<!-- Solid color as background -->
<!--            <div class="cover-bg pos-abs full-size bg-color" data-bgcolor="rgb(59, 59, 59)"></div>-->
			
			<!-- Solid color as filter -->
            <div class="cover-bg-mask pos-abs full-size bg-color" data-bgcolor="rgba(0, 0, 0, 0.41)"></div>
            
        </div>
        <!--END OF site Cover -->
		
		<!-- Begin of timer pane -->
		<div class="pane-when " id="s-when">
			<div class="content">
				<!-- Clock -->
				<div class="clock clock-countdown">
					<div class="site-config"
						 data-date="08/15/2025 10:00:00"
						 data-date-timezone="+1"
						 ></div>
					<div class="elem-center">
						<div class="digit">
							<span class="days">00</span>
							<span class="txt">jours</span>
						</div>
					</div>
					<!-- Logo instead -->
					<div class="logo">
						<a href="#">
							<img src="img/logo_only.png" alt="Golden Sky Home Logo">
						</a>
					</div>
					
					<div class="elem-bottom">
						<div class="deco"></div>
						
<!--						<span class="days">12</span><span class="thin">D</span>-->
						<span class="hours">00</span><span class="thin">H</span>
						<span class="minutes">00</span><span class="thin">MN</span>
						<span class="seconds">36</span><span class="thin">S</span>
					</div>
				</div> 
				
				
				<footer>
					<p>Avant l'ouverture de notre <strong>Appart Hôtel Golden Sky Home</strong></p>
					<p class="opening-date"><i class="timer-icon ion ion-calendar"></i>Ouverture le <strong>15 août 2025 à 10h00</strong></p>
				</footer>
			</div> 
		</div>
		<!-- End of timer pane -->
        
        <!-- BEGIN OF site main content content here -->
        <main class="page-main" id="mainpage">             
            
			<!-- Begin of home page -->
			<div class="section page-home page page-cent" id="s-home">
				
				<!-- Logo -->
				<!--<div class="logo-container">
					<img class="h-logo" src="img/logo_only.png" alt="Logo">
				</div>-->
				<!-- Content -->
				<section class="content">

					<header class="header">
						<div class="h-left">
							<h2>Appart Hôtel <strong>Golden Sky Home</strong></h2>
						</div>
						<div class="h-right">
							<h3>Café & <br>Restaurant</h3>
							<h4 class="subhead"><a href="#register">Bientôt disponible</a></h4>
						</div>
					</header>
				</section>
				
				<!-- Scroll down button -->
                <footer class="p-footer p-scrolldown">
                    <a href="#register">
                        <div class="arrow-d">
							<div class="before">Scroll</div>
							<div class="after">Down</div>
							<div class="circle"><i class="ion ion-mouse"></i></div>
						</div>
                    </a>                        
                </footer>
			</div>
			<!-- End of home page -->
            
           
            
            <!-- Begin of register page -->
            <div class="section page-register page page-cent"  id="s-register">
                <section class="content">
                    <header class="p-title">
                        <h3>Réservation <i class="ion ion-compose"></i></h3>
						<h4 class="subhead">Inscrivez-vous pour recevoir nos dernières nouvelles</h4>
                    </header>
                    <div>
                        <form id="mail-subscription" class="form magic send_email_form" method="get" action="https://demo.highhay.com/timex/ajaxserver/serverfile.php">
                            <p class="invite">Veuillez saisir votre email ci-dessous pour rester en contact avec nous :</p>
							<div class="fields clearfix">
								<div class="input">
									<label for="reg-email">Email </label>
									<input id="reg-email" class="email_f"  name="email" type="email" required placeholder="<EMAIL>" data-validation-type="email"></div>
								<div class="buttons">
									<button id="submit-email" class="button email_b" name="submit_email">S'inscrire</button>
								</div>
							</div>

                            <p class="email-ok invisible"><strong>Merci</strong> pour votre inscription. Nous vous tiendrons informé.</p>
                        </form>
                    </div>
                </section>
                <footer class="p-footer p-scrolldown">
                    <a href="#about-us">
                        <div class="arrow-d">
							<div class="before">About</div>
							<div class="after">Lorem</div>
							<div class="circle"><i class="ion ion-mouse"></i></div>
						</div>
                    </a>                        
                </footer>
            </div>
            <!-- End of register page -->
            
            <!-- Begin of about us page -->
            <div class="section page-about page page-cent" id="s-about-us">
                <section class="content">
                    <header class="p-title">
                        <h3>À Propos<i class="ion ion-android-information">
                            </i>
                        </h3>
						<h4 class="subhead">Nous créons des <span class="bold">expériences</span> <span class="bold">exceptionnelles</span></h4>
                    </header>
                    <article class="text">
                        <div class="intro-section">
                            <div class="intro-hero">
                                <div class="intro-item">
                                    <div class="intro-icon-wrapper">
                                        <i class="intro-icon ion ion-ios-location"></i>
                                    </div>
                                    <div class="intro-content">
                                        <h5 class="intro-title">Béni Mellal, Maroc</h5>
                                        <p>L'<strong>Appart Hôtel Golden Sky Home – Café & Restaurant</strong> vous ouvrira bientôt ses portes dans un cadre prestigieux, au cœur de Béni Mellal.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="services-section">
                            <div class="services-grid">
                                <div class="service-item">
                                    <div class="service-icon-wrapper">
                                        <i class="service-icon ion ion-ios-home"></i>
                                    </div>
                                    <div class="service-content">
                                        <h6 class="service-title">Appartements de Luxe</h6>
                                        <p>Profitez du luxe et de l'intimité de nos appartements entièrement équipés, associés aux prestations haut de gamme d'un hôtel de standing.</p>
                                    </div>
                                </div>

                                <div class="service-item">
                                    <div class="service-icon-wrapper">
                                        <i class="service-icon ion ion-fork"></i>
                                    </div>
                                    <div class="service-content">
                                        <h6 class="service-title">Café & Restaurant</h6>
                                        <p>Découvrez notre <strong>café-restaurant raffiné</strong>, où chaque plat est pensé comme une expérience sensorielle unique.</p>
                                    </div>
                                </div>

                                <div class="service-item">
                                    <div class="service-icon-wrapper">
                                        <i class="service-icon ion ion-leaf"></i>
                                    </div>
                                    <div class="service-content">
                                        <h6 class="service-title">Bien-être & Spa</h6>
                                        <p>Notre <strong>espace bien-être & spa</strong> vous accueille dans une ambiance sereine, propice à la relaxation absolue.</p>
                                    </div>
                                </div>

                                <div class="service-item">
                                    <div class="service-icon-wrapper">
                                        <i class="service-icon ion ion-android-favorite"></i>
                                    </div>
                                    <div class="service-content">
                                        <h6 class="service-title">Soins Premium</h6>
                                        <p>Massages, soins esthétiques, hammam et plus encore... tout est pensé pour votre bien-être et votre détente.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="signature-section">
                        </div>
                    </article>
                </section>
                <footer class="p-footer p-scrolldown">
                    <a href="#contact">
                        <div class="arrow-d">
							<div class="before">Contact</div>
							<div class="after">Message</div>
							<div class="circle"><i class="ion ion-mouse"></i></div>
						</div>
                    </a>                        
                </footer>
            </div>
            <!-- End of about us page -->
                
            <!-- Begin of Contact page   -->
            <div class="section page-contact page page-cent  bg-color" data-bgcolor="rgba(95, 25, 208, 0.88)s" id="s-contact">
				<!-- Begin of contact information -->
				<div class="slide" id="s-information" data-anchor="information">
					<section class="content">
						<header class="p-title">
							<h3>Contact<i class="ion ion-location">
								</i>
							</h3>
							<ul class="buttons">
								<li class="show-for-medium-up">
									<a title="About" href="#about-us" ><i class="ion ion-android-information"></i></a>
								</li>
								<!--<li>
									<a title="Contact" href="#contact/information"><i class="ion ion-location"></i></a>
								</li>-->
								<li>
									<a title="Message" href="#contact/message"><i class="ion ion-email"></i></a>
								</li>
							</ul>
						</header>
						<!-- Begin Of Page SubSction -->
						<div class="contact">
							<div class="row">
								<div class="medium-6 columns left">
									<ul>
										<li>
											<h4><i class="contact-icon ion ion-email"></i>Email</h4>
											<p><a href="mailto:<EMAIL>"><EMAIL></a></p>
										</li>
										<li>
											<h4><i class="contact-icon ion ion-ios-location"></i>Adresse</h4>
											<p>N°02 Boulevard Mohammed V
											<br>Béni Mellal 23040 – Maroc</p>
										</li>
										<li>
											<h4><i class="contact-icon ion ion-ios-telephone"></i>Téléphone</h4>
											<p><a href="tel:+212523420071">+212 5234-20071</a></p>
										</li>
									</ul>
								</div>
								<div class="medium-6 columns social-links right">
									<ul>
										<li class="show-for-medium-up">
											<h4><i class="contact-icon ion ion-calendar"></i>Ouverture</h4>
											<p><strong>15 août 2025 à 10h00</strong></p>
										</li>
										<li class="show-for-medium-up">
											<h4><i class="contact-icon ion ion-chatbubbles"></i>Langues</h4>
											<p>Français • English</p>
										</li>
										<li  class="show-for-medium-up">
											<h4><i class="contact-icon ion ion-social-buffer"></i>Suivez-nous</h4>
											<!-- Begin of Social links -->
											<div class="socialnet">
												<a href="#" title="Facebook"><i class="ion ion-social-facebook"></i></a>
												<a href="#" title="Instagram"><i class="ion ion-social-instagram"></i></a>
												<a href="#" title="Twitter"><i class="ion ion-social-twitter"></i></a>
												<a href="#" title="LinkedIn"><i class="ion ion-social-linkedin"></i></a>
											</div>
											<!-- End of Social links -->
										</li>
										<li>
											<p><img src="img/logo_large.png" alt="Golden Sky Home Logo" class="logo"></p>
											<p class="small">© 2025 <strong>Appart Hôtel Golden Sky Home</strong>. Tous droits réservés.</p>
										</li>
									</ul>

								</div>
							</div>
						</div>
						<!-- End of page SubSection -->
					</section>  
				</div>
				<!-- end of contact information -->
				
				<!-- begin of contact message --> 
				<div class="slide" id="s-message" data-anchor="message">
					<section class="content">
						<header class="p-title">
							<h3>Write to us<i class="ion ion-email">
								</i>
							</h3>
							<ul class="buttons">
								<li class="show-for-medium-up">
									<a title="About" href="#about-us"><i class="ion ion-android-information"></i></a>
								</li>
								<li>
									<a title="Contact" href="#contact/information"><i class="ion ion-location"></i></a>
								</li>
								<!--<li>
									<a title="Message" href="#contact/message"><i class="ion ion-email"></i></a>
								</li>-->
							</ul>
						</header>
						<!-- Begin Of Page SubSction -->
						
						<div class="page-block c-right v-zoomIn">
							<div class="wrapper">
								<div>
									<form class="message form send_message_form" method="get" action="https://demo.highhay.com/timex/ajaxserver/serverfile.php">
										<div class="fields clearfix">
											<div class="input">
												<label for="mes-name">Name </label>
												<input id="mes-name" name="name" type="text" placeholder="Your Name" required></div>
											<div class="buttons">
												<button id="submit-message" class="button email_b" name="submit_message">Ok</button>
											</div>
										</div>
										<div class="fields clearfix">
											<div class="input">
												<label for="mes-email">Email </label>
												<input id="mes-email" type="email" placeholder="Email Address" name="email" required>
											</div>
										</div>
										<div class="fields clearfix no-border">
											<label for="mes-text">Message </label>
											<textarea id="mes-text" placeholder="Message ..." name="message" required></textarea>

											<div>
												<p class="message-ok invisible">Your message has been sent, thank you.</p>
											</div>
										</div>
									</form>
								</div>
							</div>
						</div>
						<!-- End Of Page SubSction -->
					</section>
						
				</div>
				<!-- End of contact message -->
            </div>
            <!-- End of Contact page  -->   
        
        </main>

        <!-- END OF site main content content here -->  
		
		<!-- Begin of site footer -->
		<footer class="page-footer">
			<div>
				<a href="http://www.facebook.com/highhay" target="_blank"><i class="ion icon ion-social-facebook"></i></a>
				<a href="http://www.instagram.com/miradontsoa" target="_blank"><i class="ion icon ion-social-instagram"></i></a>
				<a href="http://twitter.com/miradontsoa" target="_blank"><i class="ion icon ion-social-twitter"></i></a>
			</div>
		</footer>
		<!-- End of site footer -->

        
<!--        <script src="//ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.min.js"></script>-->
        
        <!-- All Javascript plugins goes here -->
<!--		<script src="//code.jquery.com/jquery-1.11.2.min.js"></script>-->
        <script src="js/vendor/jquery-1.11.2.min.js"></script>
		<!-- All vendor scripts -->
        <script src="js/vendor/all.js"></script>
		
		<!-- Detailed vendor scripts -->
        <!--<script src="./js/vendor/jquery.fullPage.min.js"></script>
        <script src="./js/vendor/jquery.slimscroll.min.js"></script>
        <script src="./js/vendor/jquery.knob.min.js"></script>
        <script src="./js/vegas/vegas.min.js"></script>
        <script src="./js/jquery.maximage.js"></script>
        <script src="./js/okvideo.min.js"></script>-->
		
		<!-- Downcount JS -->
        <script src="js/jquery.downCount.js"></script>
		
		<!-- Form script -->
        <script src="js/form_script.js"></script>

        <!-- Language switcher script -->
        <script src="js/language-switcher.js"></script>

		<!-- Javascript main files -->
        <script src="js/main.js"></script>
         

        <!-- Google Analytics: Uncomment and change UA-XXXXX-X to be your site"s ID. -->
        <!--<script>
            (function(b,o,i,l,e,r){b.GoogleAnalyticsObject=l;b[l]||(b[l]=
            function(){(b[l].q=b[l].q||[]).push(arguments)});b[l].l=+new Date;
            e=o.createElement(i);r=o.getElementsByTagName(i)[0];
            e.src="//www.google-analytics.com/analytics.js";
            r.parentNode.insertBefore(e,r)}(window,document,"script","ga"));
            ga("create","UA-XXXXX-X");ga("send","pageview");
        </script>-->
    </body>

<!-- Mirrored from demo.highhay.com/timex/index.html by HTTrack Website Copier/3.x [XR&CO'2014], Fri, 25 Jul 2025 19:01:32 GMT -->
</html>
