/**
 * Golden Sky Home - Multilingual Language Manager
 * Comprehensive language switching system for French/English support
 */

(function() {
    'use strict';

    // Language configuration
    const LANGUAGES = {
        fr: 'Français',
        en: 'English'
    };

    const DEFAULT_LANGUAGE = 'fr';
    let currentLanguage = DEFAULT_LANGUAGE;

    // Comprehensive translation dictionary
    const translations = {
        // Meta and page titles
        pageTitle: {
            fr: 'Appart Hôtel Golden Sky Home – Café & Restaurant | Ouverture Prochaine',
            en: 'Golden Sky Home Aparthotel – Café & Restaurant | Coming Soon'
        },
        metaDescription: {
            fr: 'Appart Hôtel Golden Sky Home – Café & Restaurant. Ouverture le 15 août 2025 à <PERSON><PERSON><PERSON>, Maroc. Réservez dès maintenant !',
            en: 'Golden Sky Home Aparthotel – Café & Restaurant. Opening August 15, 2025 in Béni Mellal, Morocco. Book now!'
        },

        // Navigation
        navAbout: {
            fr: 'à propos',
            en: 'about'
        },
        navContact: {
            fr: 'contact',
            en: 'contact'
        },

        // Countdown section
        countdownBefore: {
            fr: 'Avant l\'ouverture de notre',
            en: 'Before the opening of our'
        },
        countdownHotelName: {
            fr: 'Appart Hôtel Golden Sky Home',
            en: 'Golden Sky Home Aparthotel'
        },
        countdownOpening: {
            fr: 'Ouverture le',
            en: 'Opening on'
        },
        countdownDate: {
            fr: '15 août 2025 à 10h00',
            en: 'August 15, 2025 at 10:00 AM'
        },
        timerDays: {
            fr: 'jours',
            en: 'days'
        },
        timerHours: {
            fr: 'H',
            en: 'H'
        },
        timerMinutes: {
            fr: 'MN',
            en: 'MIN'
        },
        timerSeconds: {
            fr: 'S',
            en: 'S'
        },

        // Main content
        hotelName: {
            fr: 'Appart Hôtel',
            en: 'Aparthotel'
        },
        hotelNameStrong: {
            fr: 'Golden Sky Home',
            en: 'Golden Sky Home'
        },
        cafeRestaurant: {
            fr: 'Café & Restaurant',
            en: 'Café & Restaurant'
        },
        comingSoon: {
            fr: 'Bientôt disponible',
            en: 'Coming Soon'
        },
        scrollDown: {
            fr: 'Scroll',
            en: 'Scroll'
        },
        scrollDownText: {
            fr: 'Down',
            en: 'Down'
        },

        // Registration section
        reservationTitle: {
            fr: 'Réservation',
            en: 'Reservation'
        },
        newsletterTitle: {
            fr: 'Inscrivez-vous pour recevoir nos dernières nouvelles',
            en: 'Subscribe to receive our latest news'
        },
        newsletterText: {
            fr: 'Veuillez saisir votre email ci-dessous pour rester en contact avec nous :',
            en: 'Please enter your email below to stay in touch with us:'
        },
        emailLabel: {
            fr: 'Email',
            en: 'Email'
        },
        subscribeButton: {
            fr: 'S\'inscrire',
            en: 'Subscribe'
        },
        aboutLorem: {
            fr: 'About',
            en: 'About'
        },
        aboutLoremText: {
            fr: 'Lorem',
            en: 'Lorem'
        },

        // About section
        aboutTitle: {
            fr: 'À Propos',
            en: 'About Us'
        },
        aboutSubtitle: {
            fr: 'Nous créons des',
            en: 'We create'
        },
        aboutSubtitleStrong1: {
            fr: 'expériences',
            en: 'exceptional'
        },
        aboutSubtitleStrong2: {
            fr: 'exceptionnelles',
            en: 'experiences'
        },
        locationTitle: {
            fr: 'Béni Mellal, Maroc',
            en: 'Béni Mellal, Morocco'
        },
        locationText: {
            fr: 'L\'Appart Hôtel Golden Sky Home – Café & Restaurant vous ouvrira bientôt ses portes dans un cadre prestigieux, au cœur de Béni Mellal.',
            en: 'The Golden Sky Home Aparthotel – Café & Restaurant will soon open its doors in a prestigious setting, in the heart of Béni Mellal.'
        },
        luxuryApartmentsTitle: {
            fr: 'Appartements de Luxe',
            en: 'Luxury Apartments'
        },
        luxuryApartmentsText: {
            fr: 'Profitez du luxe et de l\'intimité de nos appartements entièrement équipés, associés aux prestations haut de gamme d\'un hôtel de standing.',
            en: 'Enjoy the luxury and privacy of our fully equipped apartments, combined with the high-end services of a prestigious hotel.'
        },
        cafeRestaurantTitle: {
            fr: 'Café & Restaurant',
            en: 'Café & Restaurant'
        },
        cafeRestaurantText: {
            fr: 'Découvrez notre café-restaurant raffiné, où chaque plat est pensé comme une expérience sensorielle unique.',
            en: 'Discover our refined café-restaurant, where each dish is designed as a unique sensory experience.'
        },
        cafeRestaurantTextStrong: {
            fr: 'café-restaurant raffiné',
            en: 'refined café-restaurant'
        },
        wellnessSpaTitle: {
            fr: 'Bien-être & Spa',
            en: 'Wellness & Spa'
        },
        wellnessSpaText: {
            fr: 'Notre espace bien-être & spa vous accueille dans une ambiance sereine, propice à la relaxation absolue.',
            en: 'Our wellness & spa area welcomes you in a serene atmosphere, conducive to absolute relaxation.'
        },
        wellnessSpaTextStrong: {
            fr: 'espace bien-être & spa',
            en: 'wellness & spa area'
        },
        premiumCareTitle: {
            fr: 'Soins Premium',
            en: 'Premium Care'
        },
        premiumCareText: {
            fr: 'Massages, soins esthétiques, hammam et plus encore... tout est pensé pour votre bien-être et votre détente.',
            en: 'Massages, beauty treatments, hammam and more... everything is designed for your well-being and relaxation.'
        },

        // Contact section
        contactTitle: {
            fr: 'Contact',
            en: 'Contact'
        },
        contactEmail: {
            fr: 'Email',
            en: 'Email'
        },
        contactAddress: {
            fr: 'Adresse',
            en: 'Address'
        },
        contactPhone: {
            fr: 'Téléphone',
            en: 'Phone'
        },
        contactOpening: {
            fr: 'Ouverture',
            en: 'Opening'
        },
        contactOpeningDate: {
            fr: '15 août 2025 à 10h00',
            en: 'August 15, 2025 at 10:00 AM'
        },
        contactLanguages: {
            fr: 'Langues',
            en: 'Languages'
        },
        contactLanguagesText: {
            fr: 'Français • English',
            en: 'French • English'
        },
        contactFollowUs: {
            fr: 'Suivez-nous',
            en: 'Follow Us'
        },
        contactCopyright: {
            fr: '© 2025 Appart Hôtel Golden Sky Home. Tous droits réservés.',
            en: '© 2025 Golden Sky Home Aparthotel. All rights reserved.'
        },

        // Contact form
        writeToUs: {
            fr: 'Écrivez-nous',
            en: 'Write to us'
        },
        nameLabel: {
            fr: 'Nom',
            en: 'Name'
        },
        messageLabel: {
            fr: 'Message',
            en: 'Message'
        },
        okButton: {
            fr: 'Ok',
            en: 'Ok'
        },

        // Social sharing (enhance existing)
        shareOpening: {
            fr: 'Partagez notre ouverture',
            en: 'Share our opening'
        },
        followSocialMedia: {
            fr: 'Suivez-nous sur nos réseaux sociaux :',
            en: 'Follow us on social media:'
        },
        copyLink: {
            fr: 'Copier',
            en: 'Copy'
        },
        linkCopied: {
            fr: 'Copié!',
            en: 'Copied!'
        }
    };

    // Language Manager Class
    class LanguageManager {
        constructor() {
            this.init();
        }

        init() {
            this.detectLanguage();
            this.setupEventListeners();
            this.applyLanguage(currentLanguage);
        }

        detectLanguage() {
            // Check URL parameter first
            const urlParams = new URLSearchParams(window.location.search);
            const urlLang = urlParams.get('lang');
            
            if (urlLang && LANGUAGES[urlLang]) {
                currentLanguage = urlLang;
                this.saveLanguagePreference(currentLanguage);
                return;
            }

            // Check localStorage
            const savedLang = localStorage.getItem('goldensky-language');
            if (savedLang && LANGUAGES[savedLang]) {
                currentLanguage = savedLang;
                return;
            }

            // Check browser language
            const browserLang = navigator.language || navigator.userLanguage;
            const langCode = browserLang.split('-')[0];
            
            if (LANGUAGES[langCode]) {
                currentLanguage = langCode;
                this.saveLanguagePreference(currentLanguage);
            }
        }

        setupEventListeners() {
            // Language switcher buttons
            document.addEventListener('click', (e) => {
                if (e.target.matches('[data-lang]')) {
                    e.preventDefault();
                    const lang = e.target.getAttribute('data-lang');
                    this.switchLanguage(lang);
                }
            });
        }

        switchLanguage(lang) {
            if (!LANGUAGES[lang] || lang === currentLanguage) return;
            
            currentLanguage = lang;
            this.applyLanguage(lang);
            this.saveLanguagePreference(lang);
            this.updateURL(lang);
            this.notifyOtherSystems(lang);
        }

        applyLanguage(lang) {
            // Update HTML lang attribute
            document.documentElement.lang = lang;
            
            // Update page title and meta description
            document.title = translations.pageTitle[lang];
            
            const metaDesc = document.querySelector('meta[name="description"]');
            if (metaDesc) {
                metaDesc.content = translations.metaDescription[lang];
            }

            // Update all translatable elements
            this.updateTranslatableElements(lang);
            
            // Update language switcher active state
            this.updateLanguageSwitcher(lang);
            
            // Update hreflang and canonical links
            this.updateSEOLinks(lang);
        }

        updateTranslatableElements(lang) {
            // Update elements with data-translate attribute
            document.querySelectorAll('[data-translate]').forEach(element => {
                const key = element.getAttribute('data-translate');
                if (translations[key] && translations[key][lang]) {
                    element.textContent = translations[key][lang];
                }
            });

            // Update elements with data-translate-html attribute (for HTML content)
            document.querySelectorAll('[data-translate-html]').forEach(element => {
                const key = element.getAttribute('data-translate-html');
                if (translations[key] && translations[key][lang]) {
                    element.innerHTML = translations[key][lang];
                }
            });

            // Update form placeholders
            document.querySelectorAll('[data-translate-placeholder]').forEach(element => {
                const key = element.getAttribute('data-translate-placeholder');
                if (translations[key] && translations[key][lang]) {
                    element.placeholder = translations[key][lang];
                }
            });

            // Update aria-labels
            document.querySelectorAll('[data-translate-aria]').forEach(element => {
                const key = element.getAttribute('data-translate-aria');
                if (translations[key] && translations[key][lang]) {
                    element.setAttribute('aria-label', translations[key][lang]);
                }
            });
        }

        updateLanguageSwitcher(lang) {
            document.querySelectorAll('[data-lang]').forEach(btn => {
                btn.classList.toggle('active', btn.getAttribute('data-lang') === lang);
            });
        }

        updateSEOLinks(lang) {
            // Update or create hreflang links
            this.updateHreflangLinks(lang);
            
            // Update canonical link
            this.updateCanonicalLink(lang);
            
            // Update Open Graph locale
            this.updateOpenGraphLocale(lang);
        }

        updateHreflangLinks(currentLang) {
            // Remove existing hreflang links
            document.querySelectorAll('link[hreflang]').forEach(link => link.remove());
            
            // Add hreflang links for all languages
            const head = document.head;
            Object.keys(LANGUAGES).forEach(lang => {
                const link = document.createElement('link');
                link.rel = 'alternate';
                link.hreflang = lang;
                link.href = this.getLanguageURL(lang);
                head.appendChild(link);
            });
            
            // Add x-default hreflang
            const defaultLink = document.createElement('link');
            defaultLink.rel = 'alternate';
            defaultLink.hreflang = 'x-default';
            defaultLink.href = this.getLanguageURL(DEFAULT_LANGUAGE);
            head.appendChild(defaultLink);
        }

        updateCanonicalLink(lang) {
            let canonical = document.querySelector('link[rel="canonical"]');
            if (!canonical) {
                canonical = document.createElement('link');
                canonical.rel = 'canonical';
                document.head.appendChild(canonical);
            }
            canonical.href = this.getLanguageURL(lang);
        }

        updateOpenGraphLocale(lang) {
            const localeMap = {
                fr: 'fr_FR',
                en: 'en_US'
            };
            
            let ogLocale = document.querySelector('meta[property="og:locale"]');
            if (ogLocale) {
                ogLocale.content = localeMap[lang];
            }
        }

        getLanguageURL(lang) {
            const baseURL = window.location.origin + window.location.pathname;
            return lang === DEFAULT_LANGUAGE ? baseURL : `${baseURL}?lang=${lang}`;
        }

        updateURL(lang) {
            const url = this.getLanguageURL(lang);
            window.history.pushState({ language: lang }, '', url);
        }

        saveLanguagePreference(lang) {
            localStorage.setItem('goldensky-language', lang);
        }

        notifyOtherSystems(lang) {
            // Notify social sharing system
            if (window.GoldenSkySharing && window.GoldenSkySharing.updateLanguage) {
                window.GoldenSkySharing.updateLanguage(lang);
            }
            
            // Dispatch custom event for other systems
            window.dispatchEvent(new CustomEvent('languageChanged', {
                detail: { language: lang, translations: translations }
            }));
        }

        getCurrentLanguage() {
            return currentLanguage;
        }

        getTranslation(key, lang = null) {
            const targetLang = lang || currentLanguage;
            return translations[key] && translations[key][targetLang] ? translations[key][targetLang] : key;
        }
    }

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        window.LanguageManager = new LanguageManager();
    });

    // Handle browser back/forward buttons
    window.addEventListener('popstate', function(event) {
        if (event.state && event.state.language) {
            window.LanguageManager.applyLanguage(event.state.language);
        }
    });

})();
