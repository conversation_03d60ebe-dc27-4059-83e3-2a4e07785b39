/**
 * Golden Sky Home - Social Media Sharing Functionality
 * Handles social media sharing, analytics tracking, and user interactions
 * Integrated with Language Manager
 */

(function() {
    'use strict';

    // Configuration
    const SITE_CONFIG = {
        url: 'https://goldenskyhome.ma/',
        title: {
            fr: 'Appart Hôtel Golden Sky Home – Café & Restaurant | Ouverture 15 août 2025',
            en: 'Golden Sky Home Aparthotel – Café & Restaurant | Opening August 15, 2025'
        },
        description: {
            fr: 'Découvrez l\'Appart Hôtel Golden Sky Home à Béni Mellal - Ouverture le 15 août 2025! Luxe, spa wellness et restaurant gastronomique.',
            en: 'Discover Golden Sky Home Aparthotel in Béni Mellal - Opening August 15, 2025! Luxury, spa wellness and gourmet restaurant.'
        },
        hashtags: '#GoldenSkyHome #BeniMellal #Morocco #LuxuryHotel #SpaWellness #Opening2025'
    };

    // Current language detection
    let currentLang = 'fr';
    
    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        initializeSocialSharing();
        detectLanguage();
    });
    
    // Listen for language changes from the language manager
    window.addEventListener('languageChanged', function(event) {
        currentLang = event.detail.language;
        updateSharingUrls();
    });

    /**
     * Initialize social sharing functionality
     */
    function initializeSocialSharing() {
        // Add event listeners to sharing buttons
        const shareButtons = document.querySelectorAll('.share-btn');
        shareButtons.forEach(button => {
            button.addEventListener('click', handleShareClick);
        });

        // Add event listeners to profile buttons
        const profileButtons = document.querySelectorAll('.profile-btn');
        profileButtons.forEach(button => {
            button.addEventListener('click', handleProfileClick);
        });

        // Set up sharing URLs
        updateSharingUrls();
    }

    /**
     * Detect current language from page or language manager
     */
    function detectLanguage() {
        // Check if language manager is available
        if (window.LanguageManager) {
            currentLang = window.LanguageManager.getCurrentLanguage();
        } else {
            // Fallback to HTML lang attribute
            const htmlLang = document.documentElement.lang;
            if (htmlLang === 'en') {
                currentLang = 'en';
            }
        }
    }

    /**
     * Handle share button clicks
     */
    function handleShareClick(event) {
        event.preventDefault();
        
        const button = event.currentTarget;
        const platform = button.getAttribute('data-platform');
        
        switch (platform) {
            case 'facebook':
                shareOnFacebook();
                break;
            case 'twitter':
                shareOnTwitter();
                break;
            case 'linkedin':
                shareOnLinkedIn();
                break;
            case 'whatsapp':
                shareOnWhatsApp();
                break;
            case 'copy':
                copyToClipboard();
                break;
        }
        
        // Track the sharing event
        trackSocialShare(platform);
    }

    /**
     * Handle profile button clicks
     */
    function handleProfileClick(event) {
        const button = event.currentTarget;
        const platform = button.getAttribute('data-platform');
        
        // Track profile visit
        trackSocialShare(platform);
    }

    /**
     * Update sharing URLs for all buttons
     */
    function updateSharingUrls() {
        const title = SITE_CONFIG.title[currentLang];
        const description = SITE_CONFIG.description[currentLang];
        const url = SITE_CONFIG.url;
        
        // Facebook
        const facebookBtn = document.querySelector('.facebook-share');
        if (facebookBtn) {
            const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
            facebookBtn.setAttribute('href', facebookUrl);
        }
        
        // Twitter
        const twitterBtn = document.querySelector('.twitter-share');
        if (twitterBtn) {
            const twitterText = `${description} ${SITE_CONFIG.hashtags}`;
            const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(twitterText)}&url=${encodeURIComponent(url)}`;
            twitterBtn.setAttribute('href', twitterUrl);
        }
        
        // LinkedIn
        const linkedinBtn = document.querySelector('.linkedin-share');
        if (linkedinBtn) {
            const linkedinUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
            linkedinBtn.setAttribute('href', linkedinUrl);
        }
        
        // WhatsApp
        const whatsappBtn = document.querySelector('.whatsapp-share');
        if (whatsappBtn) {
            const whatsappText = `${description} ${url}`;
            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(whatsappText)}`;
            whatsappBtn.setAttribute('href', whatsappUrl);
        }
    }

    /**
     * Share on Facebook
     */
    function shareOnFacebook() {
        const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(SITE_CONFIG.url)}`;
        openShareWindow(url, 'facebook');
    }

    /**
     * Share on Twitter
     */
    function shareOnTwitter() {
        const text = `${SITE_CONFIG.description[currentLang]} ${SITE_CONFIG.hashtags}`;
        const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(SITE_CONFIG.url)}`;
        openShareWindow(url, 'twitter');
    }

    /**
     * Share on LinkedIn
     */
    function shareOnLinkedIn() {
        const url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(SITE_CONFIG.url)}`;
        openShareWindow(url, 'linkedin');
    }

    /**
     * Share on WhatsApp
     */
    function shareOnWhatsApp() {
        const text = `${SITE_CONFIG.description[currentLang]} ${SITE_CONFIG.url}`;
        const url = `https://wa.me/?text=${encodeURIComponent(text)}`;
        
        // For mobile devices, try to open WhatsApp app
        if (isMobileDevice()) {
            window.location.href = url;
        } else {
            openShareWindow(url, 'whatsapp');
        }
    }

    /**
     * Copy link to clipboard
     */
    function copyToClipboard() {
        const copyBtn = document.querySelector('.copy-link-btn');
        
        if (navigator.clipboard && window.isSecureContext) {
            // Modern clipboard API
            navigator.clipboard.writeText(SITE_CONFIG.url).then(function() {
                showCopySuccess(copyBtn);
            }).catch(function(err) {
                console.error('Failed to copy: ', err);
                fallbackCopyToClipboard(SITE_CONFIG.url);
            });
        } else {
            // Fallback for older browsers
            fallbackCopyToClipboard(SITE_CONFIG.url);
        }
    }

    /**
     * Fallback copy to clipboard method
     */
    function fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            const copyBtn = document.querySelector('.copy-link-btn');
            showCopySuccess(copyBtn);
        } catch (err) {
            console.error('Fallback: Oops, unable to copy', err);
            const message = currentLang === 'fr' ? 'Impossible de copier le lien' : 'Unable to copy link';
            alert(message);
        }
        
        document.body.removeChild(textArea);
    }

    /**
     * Show copy success animation
     */
    function showCopySuccess(button) {
        button.classList.add('copy-success');
        
        // Show success message
        const originalText = button.innerHTML;
        const successText = currentLang === 'fr' ? 
            '<i class="ion ion-checkmark"></i><span>Copié!</span>' : 
            '<i class="ion ion-checkmark"></i><span>Copied!</span>';
        
        button.innerHTML = successText;
        
        setTimeout(() => {
            button.classList.remove('copy-success');
            button.innerHTML = originalText;
        }, 2000);
    }

    /**
     * Open share window
     */
    function openShareWindow(url, platform) {
        const width = 600;
        const height = 400;
        const left = (window.innerWidth - width) / 2;
        const top = (window.innerHeight - height) / 2;
        
        const features = `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`;
        
        window.open(url, `share-${platform}`, features);
    }

    /**
     * Check if device is mobile
     */
    function isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    /**
     * Track social sharing events
     */
    function trackSocialShare(platform) {
        // Google Analytics 4
        if (typeof gtag !== 'undefined') {
            gtag('event', 'share', {
                'method': platform,
                'content_type': 'website',
                'item_id': 'golden-sky-home-coming-soon',
                'language': currentLang
            });
        }
        
        // Google Analytics Universal (fallback)
        if (typeof ga !== 'undefined') {
            ga('send', 'event', 'Social', 'Share', platform);
        }
        
        // Fallback analytics
        if (window.socialAnalytics) {
            window.socialAnalytics.track('share', platform);
        }
        
        // Console log for debugging
        console.log(`Social share tracked: ${platform} (${currentLang})`);
    }

    // Expose functions globally for language manager integration
    window.GoldenSkySharing = {
        updateLanguage: function(lang) {
            currentLang = lang;
            updateSharingUrls();
        },
        getCurrentLanguage: function() {
            return currentLang;
        }
    };

})();
