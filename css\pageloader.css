/*----------------------------------------------------------------------
Page loader CSS BY HighHay

USAGE:
------
- Add this as an external CSS file and put it before other css links inside
html file
- Add the following tag inside HTML file :
<div class='page-loader'>
    <div><i class='fa fa-spinner  fa-spin'></i><p>loading</p></div>
</div>
------------------------------------------------------------------------*/
.page-loader{
    position: fixed;
    z-index: 200;
    width: 100%;
    height: 100%;
    top:0;
    left:0;
    background: #333333;
    display: block;  
    
}
.page-loader .fa{
    font-size: 2.5rem;
}
.page-loader,
.page-loader.visible{
    -webkit-transition: 0.6s;
    -ms-transition: 0.6s;
    -moz-transition: 0.6s;
    transition: 0.6s;
    opacity: 1;
    visibility: visible;    
}
.page-loader.hidden{
    visibility: hidden;
/*    display: none;*/
    opacity: 0;
    
}
    .page-loader div{
        position: absolute;
        top:50%;
        width: 100%;
        text-align: center;
        color: #fff;
        margin-top: -1rem;
        font-size: 4rem;
    }
        .page-loader div p{
            margin-top: 1.0rem;
            font-size: 1.0rem;
            color: #fff;
            letter-spacing: -0.05rem;
            padding-left: 0.15rem;
            text-transform: uppercase;
            font-family: 'OpenSans'; 
        }